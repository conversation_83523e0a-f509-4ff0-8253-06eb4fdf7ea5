# this subroutine will set the host header right before we go to true origin (and not the shield)
#   see: https://github.com/GannettDigital/paas-vcl/blob/master/FASTLY_LESSONS_LEARNED.md#overriding-of-host-header for details
sub set_hostheader_for_origin {
  if ( (!req.backend.is_shield) && req.http.x-origin-expected-host ){
    set bereq.http.host = req.http.x-origin-expected-host;
  }
}

sub select_geo_specific_region {
  if (server.identity ~ "-IAD$") {
    set req.http.geo_region = "east";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "IAD block";
    call shared_helpers_general_record_object_path;
  } elsif (server.identity ~ "-BFI$") {
    set req.http.geo_region = "west";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "BFI block";
    call shared_helpers_general_record_object_path;
  } else {
    set req.http.geo_region = "east";
    set req.http.Gannett-Debug-Path-Item = "server_identity: " server.identity "east block";
    call shared_helpers_general_record_object_path;
    # regions are not very specific for North America (Canada) so use pops, use server.region for APAC and Asia and catch any new US-West POPs
    if ((server.datacenter ~ "^(DEN|BUR|LAX|LGB|PAO|SJC|BFI|MDW|PWK|YVR)$") || (server.region ~ "^(APAC|Asia|US-West)$")) {
      set req.http.geo_region = "west";
      set req.http.Gannett-Debug-Path-Item = "west block";
      call shared_helpers_general_record_object_path;
    }
  }
}

sub announce_region_selection {
     set req.http.Gannett-Debug-Path-Item = "region: " req.http.geo_region;
     call shared_helpers_general_record_object_path;
}

