terraform {
  required_providers {
    fastly = {
      source  = "fastly/fastly"
      version = "6.1.0"
    }
  }
}

variable "fastly_name" {
  type    = string
  default = "puzzles.usatoday.com"
}

variable "team" {
  type    = string
  default = "SRE"
}

variable "libraries" {
  type    = string
  default = "shared_helpers_general,shared_proxy_gannett,shared_eu"
}

# Not a must have, needs for pipeline or s3 loggings
variable "s3_access_key_cust_log" {
  type    = string
  default = "default_access"
}

# Not a must have, needs for pipeline or s3 logging
variable "s3_secret_key_cust_log" {
  type    = string
  default = "default_secret"
}

variable "FASTLY_MON_BQ_EMAIL" {
  type = string
}

variable "FASTLY_MON_BQ_SECRET_KEY" {
  type = string
}

resource "fastly_service_vcl" "fastly" {
  # fastly service name, should be match with the vcl repo name
  name    = terraform.workspace == "production" ? var.fastly_name : format("%s-%s", terraform.workspace, var.fastly_name)
  comment = format("Managed by %s @ https://github.com/GannettDigital/%s-vcl; %s; %s", var.team, var.fastly_name, var.libraries, terraform.workspace)

  # guard us from destroying production
  force_destroy = terraform.workspace == "production" ? false : true

  # Main domain, changes depending on environment.
  # For non-production environment, prepend environment name. ie: staging-puzzles.usatoday.com
  domain {
    name = terraform.workspace == "production" ? var.fastly_name : format("%s-%s", terraform.workspace, var.fastly_name)
  }

  domain {
    name = terraform.workspace == "origin-staging" ? "crosswords-staging-origin.gannettdigital.com" : format("%s-%s", terraform.workspace, "puzzles.usatoday.com")
  }

  domain {
    name = terraform.workspace == "origin-staging" ? "games.usatoday.com" : format("%s-%s", terraform.workspace, "games.usatoday.com")
  }

  condition {
    name      = "no_default_host"
    statement = "!req.url"
    priority  = 0
    type      = "REQUEST"
  }

  # attached to gcs logging to prevent fastly from sending a log ( instead, we log in vcl )
  # we still need to configure it so vcl can hook into this logger
  condition {
    name      = "no_logging"
    statement = "!req.url"
    priority  = 0
    type      = "RESPONSE"
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-east1-suh2mjo3oa-ue.a.run.app"
    name                  = "puzzles-east"
    shield                = "iad-va-us"
    healthcheck           = "puzzles-east-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 80000
    first_byte_timeout    = 80000
    connect_timeout       = 8000
    override_host         = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-east1-suh2mjo3oa-ue.a.run.app"
  }

  backend {
    address               = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-west1-suh2mjo3oa-uw.a.run.app"
    name                  = "puzzles-west"
    shield                = "bfi-wa-us"
    healthcheck           = "puzzles-west-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = "*.usatoday.com"
    ssl_cert_hostname     = "*.a.run.app"
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    between_bytes_timeout = 80000
    first_byte_timeout    = 80000
    connect_timeout       = 8000
    override_host         = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-west1-suh2mjo3oa-uw.a.run.app"
  }

  healthcheck {
    name              = "puzzles-east-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-east1-suh2mjo3oa-ue.a.run.app"
    path              = "/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  healthcheck {
    name              = "puzzles-west-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "crosswords-us-east1-7tpppv3ona-ue.a.run.app" : "crosswords-us-west1-suh2mjo3oa-uw.a.run.app"
    path              = "/api/serviceStatus"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  # GUPAS endpoint used by the WALL*LY CAM code.
  backend {
    address               = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    name                  = "GUPAS"
    shield                = "iad-va-us"
    healthcheck           = "GUPAS-healthcheck"
    port                  = 443
    use_ssl               = true
    ssl_check_cert        = true
    ssl_sni_hostname      = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    ssl_cert_hostname     = "*.gannettdigital.com"
    request_condition     = "no_default_host"
    between_bytes_timeout = terraform.workspace == "origin-staging" ? 30000 : 1000
    first_byte_timeout    = terraform.workspace == "origin-staging" ? 30000 : 1000
    connect_timeout       = terraform.workspace == "origin-staging" ? 8000 : 1000
    auto_loadbalance      = false
  }

  healthcheck {
    name              = "GUPAS-healthcheck"
    host              = terraform.workspace == "origin-staging" ? "gupas-staging.gannettdigital.com" : "gupas.gannettdigital.com"
    path              = "/status/"
    expected_response = 200
    method            = "GET"
    check_interval    = 60000
    threshold         = 1
    timeout           = 5000
    window            = 2
    initial           = 1
  }

  backend {
    address           = "www.gannett-cdn.com"
    name              = "noshield-www.gannett-cdn.com"
    port              = 443
    use_ssl           = true
    ssl_check_cert    = true
    ssl_sni_hostname  = "www.gannett-cdn.com"
    ssl_cert_hostname = "www.gannett-cdn.com"
    request_condition = "no_default_host"
    auto_loadbalance  = false
  }

  # gcias backend
  backend {
    address               = "gcias-compute.usatoday.com"
    name                  = "gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "gcias-compute.usatoday.com"
    ssl_sni_hostname      = "gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = "staging-gcias-compute.usatoday.com"
    name                  = "staging-gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "staging-gcias-compute.usatoday.com"
    ssl_sni_hostname      = "staging-gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  backend {
    address               = "ci-gcias-compute.usatoday.com"
    name                  = "ci-gcias-compute.usatoday.com"
    port                  = 443
    use_ssl               = true
    request_condition     = "no_default_host"
    auto_loadbalance      = false
    ssl_cert_hostname     = "ci-gcias-compute.usatoday.com"
    ssl_sni_hostname      = "ci-gcias-compute.usatoday.com"
    between_bytes_timeout = 30000
    first_byte_timeout    = 30000
    connect_timeout       = 6000
  }

  logging_bigquery {
    name       = "bigquery-logs"
    dataset    = "sre_fastly_logs"
    project_id = "gannett-sre-monitoring"
    table      = terraform.workspace == "production" ? "sre_fastly_logs_common" : "sre_fastly_logs_common_staging"
    email      = var.FASTLY_MON_BQ_EMAIL
    secret_key = var.FASTLY_MON_BQ_SECRET_KEY
  }

  vcl {
    name    = "main"
    content = file("${path.module}/main.vcl")
    main    = true
  }

  vcl {
    name    = "redirects"
    content = file("${path.module}/redirects.vcl")
    main    = false
  }

  vcl {
    name    = "tables"
    content = file("${path.module}/tables.vcl")
    main    = false
  }

  vcl {
    name    = "helpers"
    content = file("${path.module}/helpers.vcl")
    main    = false
  }

  vcl {
    name    = "abtest"
    content = file("${path.module}/abtest.vcl")
    main    = false
  }

  vcl {
    name    = "wall-ly"
    content = file("${path.module}/lib/wall-ly/vcl/wall-ly.vcl")
    main    = false
  }

  vcl {
    name    = "guplib"
    content = file("${path.module}/lib/guplib/vcl/guplib.vcl")
    main    = false
  }

  dynamicsnippet {
    name     = "shared_helpers_general"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_proxy_gannett"
    type     = "none"
    priority = 10
  }

  dynamicsnippet {
    name     = "shared_eu"
    type     = "none"
    priority = 10
  }

  dictionary {
    name = "puzzles_cam_settings"
  }

  # See https://github.com/GannettDigital/wall-ly#readme for info on the following wally_sso_* tables
  dictionary {
    name = "wally_sso_settings"
  }

  dictionary {
    name = "wally_sso_always_current_hostnames"
  }

  dictionary {
    name = "wally_sso_roadblock_check_hostnames"
  }

  dictionary {
    name       = "wally_sso_crypt_key"
    write_only = true
  }

  dictionary {
    name       = "wally_sso_crypt_iv"
    write_only = true
  }

  # arkadium redirects
  dictionary {
    name = "arkadium_redirects"
  }

  dictionary {
    name = "weight_shift"
  }

  dictionary {
    name = "puzzles_states"
  }
  dictionary {
    name = "puzzles_redirects"
  }

  dictionary {
    name = "wally_dynamic_settings"
  }

  ###############################################
  ####################-NGWAF-####################
  ###############################################

  #### NGWAF Dynamic Snippets - MANAGED BY FASTLY - Start
  dynamicsnippet {
    name     = "ngwaf_config_init"
    type     = "init"
    priority = 0
  }
  dynamicsnippet {
    name     = "ngwaf_config_miss"
    type     = "miss"
    priority = 9000
  }
  dynamicsnippet {
    name     = "ngwaf_config_pass"
    type     = "pass"
    priority = 9000
  }
  dynamicsnippet {
    name     = "ngwaf_config_deliver"
    type     = "deliver"
    priority = 9000
  }
  #### NGWAF Dynamic Snippets - MANAGED BY FASTLY - End

  dictionary {
    name = "Edge_Security"
  }

}

output "service_id" {
  value = fastly_service_vcl.fastly.id
}

output "service_address" {
  value = terraform.workspace == "production" ? format("%s.global.prod.fastly.net", var.fastly_name) : format("%s-%s.global.prod.fastly.net", terraform.workspace, var.fastly_name)
}

output "active_version" {
  value = fastly_service_vcl.fastly.active_version
}
