package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestReporting(t *testing.T) {
	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "should include NEL and report-to network error logging headers in the response",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/",
				UA:          "",
				Referer:     "",
				KeepDomain:  true,
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Nel": []string{
					"success_fraction",
				},
				"Report-To": []string{
					"reporting-api.gannettinnovation.com",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}
	for _, test := range tests {
		test.Execute(t)
	}
}
