// parameters are specific to deploy fastly in sre jenkins
// if you are using your own jenkins, please import library platform-engineering-plumbing 
// and change the values accordingly
fastlyDeploy {
  packageName = "puzzles.usatoday.com"
  fastlyVaultPath = "secret/sre/fastly"
  terraformVaultPath = "secret/sre/terraform"
  slackVaultPath = "secret/sre/slack/production"
  quayVaultPath = "secret/sre/quay"
  githubSSHKey = "gannett-paas-success"
  slackChannel = "#fastly-deploy"
  ephemeralWorkerLabel = 'kubernetes'
  environment = "production"
  serviceID = "59fCdMrCqLrvys6an8jjiX"
  sourceService = "59fCdMrCqLrvys6an8jjiX"
  newTerraform = true
}