package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestHSTS(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "Test hsts header is set for puzzles",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Strict-Transport-Security": []string{
					"max-age=63072000",
					"includeSubDomains",
					"preload",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
