package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestGCDN(t *testing.T) {
	t.<PERSON>()
	var tests = []fastly.Test{
		fastly.Status{
			Request: fastly.Request{
				Description: "/gcdn path responds back with 200",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/gcdn/sdgt-games-static-content/images/bg-subscribe-box.png",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "/gcdn header test",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/gcdn/sdgt-games-static-content/images/bg-subscribe-box.png",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.<PERSON>ie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Surrogate-Key": []string{
					"gcdn_/sdgt-games-static-content/images",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	for _, test := range tests {
		test.Execute(t)
	}
}
