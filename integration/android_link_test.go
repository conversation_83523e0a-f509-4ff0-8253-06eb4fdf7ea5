package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestAndroidLink(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "puzzles.usatoday.com android json check",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/.well-known/assetlinks.json",
				UA:          "",
				Referer:     "",
				KeepDomain:  true,
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path-Full": []string{
					"storage.googleapis.com backend",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}
	for _, test := range tests {
		test.Execute(t)
	}
}
