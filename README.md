# puzzles.usatoday.com
This repository is for puzzles.usatoday.com for fastly repo including terraform, vcl, integration test and ci.

1. puzzles.usatoday.com (59fCdMrCqLrvys6an8jjiX) - production service
2. staging-puzzles.usatoday.com (7OU6MaUYcyCi7saxkczsOz) - staging service that points to production origin
3. origin-staging-puzzles.usatoday.com (6oj8FX40Ud7wQnYrBo65L2) - staging service that points to staging origin 

## Backend Application Repo

https://github.com/GannettDigital/crosswords

Author/Maintainer: <EMAIL>

## Standards for Contributing

This is a heavy-traffic repository. While we try to respond to pull requests as quickly as possible, due to the volume we have a 24 hour SLA for any incoming simple redirect requests to review or merge a PR. In order for us to honor this SLA, we ask contributors do a couple things:
* Make a ticket for any pull request that you want merged. Without a ticket, we can't track the time it takes to review, iterate, and deploy changes. The body of the ticket should look something like this:
`I need X PR reviewed, merged, and in production by Y date.` Where Y is at least 24 hours away from ticket creation date. 
* For complex logic requests that you don't intend to PR yourself (ie. more than a simple redirect), please make a ticket early enough for us to add it to our next sprint. We cannot honor our 24 hour SLA for these requests.
* Post to #ask-platform-eng slack's channel using the **request help** workflow, assign the appropiate severity and fastly as component. Add the PR link or the ticket (which in return has a link to the PR). Lastly, use SRE or SRE1 reactions to notify the team (although it is not mandatory). 
* Do not directly ping maintainers of the repository to get your pull requests prioritized. Instead, post in #ask-platform-eng with the appropiate severity.


## Testing

Tests are in `integration/` and grouped by feature or path being tested. If test tables have a dozen or more tests, consider splitting tests into multiple test files.

Utility helpers and content check constants are in `integration/utils*.go` files. Constants have been set for:

* User agents with naming pattern `UserAgent{OS/DEVICE}{BROWSER}`
* Web page strings to match for content on UW, UX, and mhigh backends with naming pattern `Content{BACKEND}{NAME}`
* Present cookies when needed for backend selection/hashing

Test methods have been standardized into these methods in `utils.go` and `utils_types.go`:
* `TestHeader` with `TestDataTextCompare` struct input
* `TestStatusCode` with `TestDataTextCompare` struct input
* `TestBody` with `TestDataStatusCode` struct input

## New Relic APM

To access newrelic, go to your OKTA home page and look for the ***New Relic*** chicklet.  
New alerts can be configured via PR in [`this repository`](https://github.com/GannettDigital/sre-terraform/tree/57fe018526c93b4f5493998141cd251adaf9e933/newrelic-monitors)