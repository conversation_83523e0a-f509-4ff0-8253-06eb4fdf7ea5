package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestAb(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "puzzles_ub cookie should be set when there is a query parameter provided",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/?puzzles-ub=20",
				UA:          "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Set-Cookie": []string{
					"gnt_ub=20",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "user bucket less than 25 should get Test A",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/?puzzles-ub=20",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.<PERSON>ie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path-Full": []string{
					"sntest_a",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "user bucket from 26 ~ 50 should get Control Test",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/?puzzles-ub=40",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path-Full": []string{
					"sntest_b",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}
	for _, test := range tests {
		test.Execute(t)
	}
}
