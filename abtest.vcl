# Test bucketing and setting of X-AbVariant.
sub abtest_recv_headers {

    if (!req.http.Gannett-AB-Puzzles:puzzles_ub) {      
        
        if (req.url.qs ~ "puzzles-ub") {
            # cleanup the existing cookies and obtain value from existing query parameter
            unset req.http.Cookie:gnt_ub;
            set req.http.Gannett-AB-Puzzles:puzzles_ub = subfield(req.url.qs, "puzzles-ub", "&");
            set req.http.Gannett-Debug-Path-Item = "override_from_qs req.http.Gannett-AB-Puzzles:puzzles_ub=" req.http.Gannett-AB-Puzzles:puzzles_ub;
            call shared_helpers_general_record_object_path;
        } else if (req.http.Cookie:gnt_ub) {
            set req.http.Gannett-AB-Puzzles:puzzles_ub = req.http.Cookie:gnt_ub;
            set req.http.Gannett-Debug-Path-Item = "from_stash req.http.Gannett-AB-Puzzles:puzzles_ub=" req.http.Gannett-AB-Puzzles:puzzles_ub " Cookie: puzzles_ub=" req.http.Cookie:gnt_ub;
            call shared_helpers_general_record_object_path;
        } else {
            # assign random value to cookie if not already set
            set req.http.Gannett-AB-Puzzles:puzzles_ub = randomint(1, 100);
        }
    }
    set req.http.Gannett-Debug-Path-Item = "Gannett-AB-Puzzles:puzzles_ub" req.http.Gannett-AB-Puzzles:puzzles_ub;
    call shared_helpers_general_record_object_path;
    
    declare local var.user_bucket INTEGER;
    set var.user_bucket = std.atoi(req.http.Gannett-AB-Puzzles:puzzles_ub);

    # Allocate 50% of users for testing subscribe now (sntest)
    if (var.user_bucket > 0 && var.user_bucket <= 50) {
        if (var.user_bucket > 0 && var.user_bucket <= 25) {
            # Designate one half of that 50% of users to get the variant
            set req.http.X-AbVariant-Puzzles = "sntest_a";
        } else {
            # Designate the other half of users to receive the control/default experience. We do this to
            # have an even split of users in a control group for more accurate analysis.
            set req.http.X-AbVariant-Puzzles = "sntest_b";
        }
        set req.http.Gannett-Debug-Path-Item = "X-AbVariant-Puzzles: " req.http.X-AbVariant-Puzzles;
        call shared_helpers_general_record_object_path;
    }
}   

# Set Vary headers.
sub abtest_fetch {
  set beresp.http.Vary:X-AbVariant-Puzzles = "";
}

# Cookie setting and cleanup.
sub abtest_deliver {

    ## Setup Cookie
    if (!req.http.Cookie:gnt_ub) {
        add resp.http.Set-Cookie = "gnt_ub=" req.http.Gannett-AB-Puzzles:puzzles_ub "; domain=.usatoday.com; path=/; secure; samesite=lax; max-age=31536000;";
    }
    ## Setup response header X-AbVariant-Puzzles
    if (req.http.X-AbVariant-Puzzles) {
        add resp.http.X-AbVariant-Puzzles = req.http.X-AbVariant-Puzzles;
    }
}
