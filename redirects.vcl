#redirects to be added as links are setup (PENG-18562)
sub arkadium_redirects {
    ### Fastly Geolocation Override for Integration Tests
    if (req.http.gannett-geo-ip-override) {
      set client.geo.ip_override = req.http.gannett-geo-ip-override;
      unset req.http.gannett-geo-ip-override;
    } else {
      set client.geo.ip_override = req.http.Fastly-Client-IP;
    }
    # setting Variable for arkadium redirects that will live post-launch
    declare local var.x_url_path STRING;
    set var.x_url_path = std.tolower(req.url.path);

    # Detect the User as coming from US or outside US and set the header for it.
    if ( (table.lookup(puzzles_states, client.geo.region, "false") == "true") || client.geo.country_code == "US" || client.geo.country_code == "PR" || client.ip ~ shared_helpers_general_office ) {
        set req.http.US-User = "1";
        set req.http.Gannett-Debug-Path-Item = "US-USER";
        call shared_helpers_general_record_object_path;
    } else {
        set req.http.US-User = "0";
        set req.http.Gannett-Debug-Path-Item = "NON-US-USER";
        call shared_helpers_general_record_object_path;
    }

    # redirect non-US Users hitting https://puzzles.usatoday.com to arkadium word
    if ( (req.url.path == "" || req.url.path == "/" || req.url.path == "/quick-cross" || req.url.path == "/sudoku") && 
    req.http.host ~ "^(origin-staging-|staging-|ci-)?puzzles\." &&
    req.http.US-User == "0" ) {
        set req.http.Gannett-Debug-Path-Item = "Arkadium homepage - REDIR";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = "https://games.usatoday.com/category/word";
        error 702 req.http.x-Redir-Url;
    } 
    # redirect US-Users going to crosswords url for SEO preservation
    else if ( req.http.US-User == "1" && (table.lookup(puzzles_redirects, var.x_url_path)) ) {
        set req.http.Gannett-Debug-Path-Item = "US-USER STATE: " client.geo.region + " Crosswords-Redir";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = table.lookup(puzzles_redirects, var.x_url_path);
        if(req.http.host ~ "^origin-staging-") {
            set req.http.x-Redir-Url = regsub (req.http.x-Redir-Url, "https://puzzles", "https://origin-staging-puzzles");
        }
        if (req.url.path !~ "^/sudoku"){
            error 701 req.http.x-Redir-Url;
        }
    } 
    # non-crosswords URL redirects for ALL Users
    else if(table.lookup(arkadium_redirects, var.x_url_path)) {
        set req.http.Gannett-Debug-Path-Item = table.lookup(arkadium_redirects, var.x_url_path) " redir";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = table.lookup(arkadium_redirects, var.x_url_path);
        error 702 req.http.x-Redir-Url;
    } 
    # handing arkadium frontend script calls to redirect back to arkadium
    else if (req.url.path ~ "^/service-worker.js" && req.http.host ~ "^(staging-|ci-)?puzzles\.") {
        set req.http.Gannett-Debug-Path-Item = "req.url redirect";
        call shared_helpers_general_record_object_path;
        set req.http.x-Redir-Url = "https://games.usatoday.com" + req.url;
        error 702 req.http.x-Redir-Url;
    } 
    # anything URLs outside of the dictionary for non-US Users should throw 404s on the edge.
    else if ( req.http.host ~ "^(staging-|ci-)?puzzles\." && req.http.US-User == "0" ) {
        error 971;
    }
    unset req.http.US-User;
}