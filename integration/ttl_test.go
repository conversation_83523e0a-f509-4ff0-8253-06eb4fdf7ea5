package integration

import (
	"net/http"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestTTL(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "check for cache/ttl settings for puzzles.usatoday.com",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "",
				UA:          "",
				Referer:     "",
				KeepDomain:  true,
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				// The no-store header is appended by the guplib-vcl library.
				"Cache-Control": []string{
					"private, no-cache, max-age=0, must-revalidate,no-store",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}
	for _, test := range tests {
		test.Execute(t)
	}
}
