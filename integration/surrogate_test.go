package integration

import (
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestSurrogate(t *testing.T) {
	t.<PERSON>llel()

	var tests = []fastly.Test{
		// fastly.Header{
		// 	Request: fastly.Request{
		// 		Description: "surrogate key for purging",
		// 		Scheme:      "https://",
		// 		Host:        "puzzles.usatoday.com",
		// 		Path:        "",
		// 		UA:          "",
		// 		Referer:     "",
		// 		KeepDomain:  true,
		// 		Cookies:     []*http.Cookie{},
		// 		Headers:     http.Header{},
		// 	},
		// 	Status: http.StatusOK,
		// 	Headers: http.Header{
		// 		"Surrogate-Key": []string{
		// 			"puzzles",
		// 		},
		// 	},
		// 	MatchType: fastly.PartialMatch{},
		// },
	}
	for _, test := range tests {
		test.Execute(t)
	}
}
