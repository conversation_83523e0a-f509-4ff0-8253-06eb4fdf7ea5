include "redirects";
include "helpers";
include "tables";
include "abtest";
include "wall-ly";
include "guplib";
include "snippet::shared_helpers_general";
include "snippet::shared_proxy_gannett";
include "snippet::shared_eu";

sub vcl_recv {
    ##### /sdgt-games-static-content/*
    if (req.url.path == "/.well-known/assetlinks.json") {
        set req.http.Gannett-Debug-Path-Item = "gannett-cdn backend: gannett-sdgt-games-prod-bucket production rule";
        call shared_helpers_general_record_object_path;
        set req.url = regsub(req.url, "^/.well-known", "/sdgt-games-static-content");
        set req.http.x-origin-expected-host = "www.gannett-cdn.com";
        set req.backend = F_noshield_www_gannett_cdn_com;
        return (pass);
    }

    if (req.http.host ~ "games.usatoday.com"){
        if (!req.http.Fastly-SSL) {
            set req.http.x-Redir-Url = "https://" req.http.host req.url;
            error 701 "Force SSL";
        }
        set req.http.x-Redir-Url = "https://eu.usatoday.com/unsupported-eu/";
        error 702 req.http.x-Redir-Url;
    }

    # Execute the following only once per request lifetime and not on the shield or restarts.
    if (fastly.ff.visits_this_service == 0 && req.restarts == 0) {
        # Create/save any GUP IDs in req.http.GUP-Identifiers
        call guplib_recv_getset_ids;
    }

    # This sets a header to determine whether this request pass is subject to CAM restrictions
    call determine_cam_enabled;

    # WALL*LY initial setup and resetting of URL after restarts.
    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        call wally_recv_early;
    }

    # dont run the arkadium logic for api calls
    if (req.url.path !~ "^/api/(query|metering)") {
        #redirects to be added as links are setup (PENG-18562)
        call arkadium_redirects;
    }

    call select_geo_specific_region;

    if (req.http.Fastly-Orig-Accept-Encoding) {
        if (req.http.User-Agent ~ "MSIE 6") {
            # For that 0.3% of stubborn users out there
            unset req.http.Accept-Encoding;
        } elsif ((req.http.Fastly-Orig-Accept-Encoding ~ "br") && (req.http.Fastly-Orig-Accept-Encoding ~ "gzip")) {
            set req.http.Accept-Encoding = "br, gzip";
        } elsif (req.http.Fastly-Orig-Accept-Encoding ~ "gzip") {
            set req.http.Accept-Encoding = "gzip";
        } else {
            unset req.http.Accept-Encoding;
        }
    }

    # remove production-puzzles from below authorization on Launch time
    if (( (req.http.host ~ "^production-puzzles" ||
        req.http.host ~ "^staging" ||
        req.http.host ~ "^ci" ) &&
        !( client.ip ~ shared_helpers_general_office) &&
        !req.http.Fastly-FF ) || req.request == "FASTLYPURGE" ) {
          if ( table.lookup(access_keys, req.http.Authorization, "NOTFOUND") == "NOTFOUND" &&
              !req.url ~ "^/(index\.php/)?####ADMIN_PATH####/" &&
              !req.url ~ "^/(index\.php/)?(rest|oauth)/" &&
              !req.url ~ "^/pub/static/" ) {
              error 971;
          }
    }

    # logic to implement our own FF headers
    if (req.http.Gannett-FF-HMAC != digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF)) {
        unset req.http.Gannett-FF-HMAC;
        unset req.http.Gannett-FF;
    }
    set req.http.vcl_data = req.vcl;
    if (req.http.Fastly-FF) {
        set req.max_stale_while_revalidate = 0s;
    }
    if (!req.http.Fastly-SSL) {
        set req.http.x-Redir-Url = "https://" req.http.host req.url;
        error 701 "Force SSL";
    }
    if (req.http.Fastly-SSL) {
        set req.http.X-gannett-protocol = "https://";
    } else {
        set req.http.X-gannett-protocol = "http://";
    }

    # shared_proxy_gannett_vcl_recv_gcdn - req.url.path ~ "^/gcdn" - return(pass|lookup) - setup backend
    call shared_proxy_gannett_vcl_recv_gcdn;
    # shared_proxy_gannett_vcl_recv_gcias - req.url.path ~ "^/gciaf" - return(pass) - setup backend
    call shared_proxy_gannett_vcl_recv_gcias;

    if (req.http.host ~ "^(origin-staging-)"){
        set req.http.Referer = "https://origin-staging-puzzles.usatoday.com";
    } else {
        # change the below referer to https://puzzles.usatoday.com for Launch time
        set req.http.Referer = "https://puzzles.usatoday.com";
    }

    if (req.http.host ~ "^origin-staging-") {
        set req.http.x-origin-expected-host = "origin-staging-puzzles.usatoday.com";
    } else {
        set req.http.x-origin-expected-host = "puzzles.usatoday.com";
    }

    # Apply actual backend selection
    if(req.http.x-origin-expected-host ~ "puzzles.usatoday.com"){
        set req.http.x-append-surrogate = "puzzles";

        declare local var.x-puzzles-weight-shift STRING;
        set var.x-puzzles-weight-shift = table.lookup(weight_shift, "puzzles", "null");

        if (var.x-puzzles-weight-shift == "null") {
            call announce_region_selection;
            if (req.http.geo_region == "east") {
                set req.backend = F_puzzles_east;
            } else {
                set req.backend = F_puzzles_west;
            }
        } else {
            if (randombool(std.atoi(var.x-puzzles-weight-shift), 100)) {
                set req.backend = F_puzzles_east;
            } else {
                set req.backend = F_puzzles_west;
            }
        }

        set req.http.Gannett-Debug-Path-Item = "puzzles.usatoday.com backend (" req.backend ") selected";
        call shared_helpers_general_record_object_path;

        # check that the backend is healthy
        if(req.backend == F_puzzles_east && !req.backend.healthy) {
            set req.backend = F_puzzles_west;
            set req.http.Gannett-Debug-Path-Item = "east unhealthy";
            call shared_helpers_general_record_object_path;
        } else if(req.backend == F_puzzles_west && !req.backend.healthy) {
            set req.backend = F_puzzles_east;
            set req.http.Gannett-Debug-Path-Item = "west unhealthy";
            call shared_helpers_general_record_object_path;
        }
    }

    # AB testing for puzzles
    if(req.http.x-origin-expected-host ~ "puzzles.usatoday.com"){
        call abtest_recv_headers;
        if (req.http.Gannett-Debug) {
            set req.http.Gannett-Debug-Path-Item = "abtest_recv_headers";
            call shared_helpers_general_record_object_path;
        }
    }

    # Set the disable foulball header for Wall*ly for all markets.
    set req.http.Gannett-Wally-Disable-Foulballs = "Yes";

    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        # WALL*LY: Figure out who the visiting user is for paywall purposes.
        call wally_recv_late;
    } else {
        # So that the WALL*LY synthetic resources can still be served when turned off.
        call wally_recv_cam_off_url_routing;
    }

    # WALL*LY: Complete paywall processing.
    # If no valid firefly_akamai cookie is found AND origin healthcheck passes, will shortcut to pass stage.
    if (req.backend.healthy) {
        if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
            call wally_recv_complete;
        }
    }

    #FASTLY recv

    if (req.request != "HEAD" && req.request != "GET" && req.method != "POST" && req.request != "FASTLYPURGE") {
        return(pass);
    }

    if (req.method == "POST" && req.url.path !~ "^/api/(query|metering)") {
        return(pass);
    }

    if (req.method == "POST") {
        if (req.url.path ~ "^/api/(query|metering)") {
            if (!req.body) {
                error 400 "Request body empty or too large (8KB limit)";
            }
            return(lookup);
        }
    }

    # Gannett Boilerplate
    # logic to implement our own FF header
    if (!req.http.Gannett-FF) {
        set req.http.Gannett-FF = server.identity;
    } else {
        set req.http.Gannett-FF = req.http.Gannett-FF + ", " + server.identity;
    }
    # secret key should match the one at the top
    set req.http.Gannett-FF-HMAC = digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF);


    if (req.http.Cookie ~ "gup_userjwt"){
        set req.http.gup_userjwt = req.http.Cookie:gup_userjwt;
    }

    return(lookup);
}

sub vcl_hash {
    set req.hash += req.url;
    set req.hash += req.http.host;


    if (req.http.jwt-token) {
        set req.hash += req.http.jwt-token;
    }

    # Allow caching for POST requests to specific endpoints
    if (req.request == "POST") {
        if (req.url.path ~ "^/api/(query|metering)") {
            set req.hash += req.body.base64;
        }
    }

    set req.hash += req.vcl.generation;

    #FASTLY hash
    return(hash);
}

sub vcl_fetch {
    if (!req.backend.is_shield) {
        set beresp.http.backend_ip = beresp.backend.ip;
    }

    if (!req.backend.is_shield) {
        if (beresp.http.Surrogate-Key) {
            if (beresp.status != 200) {
                set beresp.http.Surrogate-Key = beresp.http.Surrogate-Key " " req.url.dirname " " beresp.status " " req.http.x-append-surrogate;
            } else{
                set beresp.http.Surrogate-Key = beresp.http.Surrogate-Key " " req.url.dirname " " req.http.x-append-surrogate;
            }
        } else {
            if (beresp.status != 200) {
                set beresp.http.Surrogate-Key = req.url.dirname " " beresp.status " " req.http.x-append-surrogate;
            } else {
                set beresp.http.Surrogate-Key = req.url.dirname " " req.http.x-append-surrogate;
            }
        }
    }

    # shared_proxy_gannett_vcl_fetch_gcdn - req.http.Gannett-Custom:gcdn - return deliver - setup ttl and cache-control
    call shared_proxy_gannett_vcl_fetch_gcdn;
    # shared_proxy_gannett_vcl_fetch_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return pass - setup ttl and cache-control
    call shared_proxy_gannett_vcl_fetch_gcias;

    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        call wally_fetch;
    }

    # Set a/b test Vary headers.
    if(req.http.x-origin-expected-host ~ "puzzles.usatoday.com") {
        call abtest_fetch;
        if (req.http.Gannett-Debug) {
            set req.http.Gannett-Debug-Path-Item = "abtest_fetch";
            call shared_helpers_general_record_object_path;
        }
    }

    #gannett boilerplate
     # gzip compression
    if ((beresp.status == 200 || beresp.status == 404) && (beresp.http.content-type ~ "^(text\/html|application\/x\-javascript|text\/css|application\/javascript|text\/javascript|application\/json|application\/vnd\.ms\-fontobject|application\/x\-font\-opentype|application\/x\-font\-truetype|application\/x\-font\-ttf|application\/xml|font\/eot|font\/opentype|font\/otf|image\/svg\+xml|image\/vnd\.microsoft\.icon|text\/plain|text\/xml)\s*($|;)" || req.url ~ "\.(css|js|html|eot|ico|otf|ttf|json|svg)($|\?)" ) ) {
        # always set vary to make sure uncompressed versions dont always win
        if (!beresp.http.Vary ~ "Accept-Encoding") {
            if (beresp.http.Vary) {
                set beresp.http.Vary = beresp.http.Vary ", Accept-Encoding";
            } else {
                set beresp.http.Vary = "Accept-Encoding";
            }
        }
        if (req.http.Accept-Encoding ~ "gzip") {
            set beresp.gzip = true;
        }
    }

     /* cache errors for 1m */
    if (beresp.status >= 300 && beresp.status < 600) {
        /* Do not cache SigSci errors */
        if(beresp.http.x-sigsci-agentresponse ~ "(406|429)") {
            return(deliver);
        }

      /* deliver stale if the object is available */
        if (stale.exists) {
            return(deliver_stale);
        }

        set beresp.http.Surrogate-Key = req.url.dirname " " beresp.status " cache_error";
        set beresp.cacheable = true;
        set beresp.ttl = 60s; # or what ever the TTL should be

        # construct our debug headers
        set beresp.http.Gannett-Debug-Path = if(beresp.http.Gannett-Debug-Path, beresp.http.Gannett-Debug-Path, req.http.Gannett-Debug-Path);
        set beresp.http.Gannett-Debug-Path-Full = if(beresp.http.Gannett-Debug-Path-Full, "restarts: " req.restarts " ttl: " beresp.ttl " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path " >>>> " beresp.http.Gannett-Debug-Path-Full, "restarts: " req.restarts " ttl: " beresp.ttl " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path);
        return(deliver);
    }

    /* set stale_if_error to a day if origin is down */
    set beresp.stale_if_error = 86400s;

    #FASTLY fetch


    if (req.method == "POST" && req.url.path ~ "^/api/(query|metering)") {
        if (beresp.http.Surrogate-Control ~ "max-age"){
            set beresp.http.Surrogate-Control = "max-age=15"; #override 120 with 15 seconds
            set beresp.ttl = 15s; # Cache in Fastly
        }else if (beresp.http.Surrogate-Control ~ "no-cache"){
            return(pass);
        }else {
            set beresp.ttl = 3600s;
        }
    }else {
        if (beresp.http.Surrogate-Control ~ "max-age"){
            set beresp.http.Surrogate-Control = "max-age=15"; #override 120 with 15 seconds
            set beresp.ttl = 15s; # Cache in Fastly
        }else {
            set beresp.ttl = 3600s;
        }
    }

    # Specifically set browser cache rules for fonts
    if (req.url.path ~ "^/webfonts/") {
        set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
    }

    if ((beresp.status == 500 || beresp.status == 503) && req.restarts < 1 && (req.method == "GET" || req.method == "HEAD")) {
        restart;
    }

    if (req.restarts > 0) {
        set beresp.http.Fastly-Restarts = req.restarts;
    }

    if (beresp.http.Set-Cookie) {
        set req.http.Fastly-Cachetype = "SETCOOKIE";
        return(pass);
    }

    # By default we set a TTL based on the `Cache-Control` header but we don't parse additional directives
    # like `private` and `no-store`.  Private in particular should be respected at the edge:
    if (beresp.http.Cache-Control ~ "(private|no-store)") {
        return(pass);
    }

    set beresp.http.Vary:gup_userjwt = "";

    if(!req.http.x-append-surrogate){
        set req.http.x-append-surrogate = "";
    }

    unset beresp.http.Expires;
    set beresp.http.Gannett-Debug-Path = if(beresp.http.Gannett-Debug-Path, beresp.http.Gannett-Debug-Path, req.http.Gannett-Debug-Path);
    set beresp.http.Gannett-Debug-Path-Full = if(beresp.http.Gannett-Debug-Path-Full, "restarts: " req.restarts  " backend: " req.backend " ttl: " beresp.ttl " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path " >>>> " beresp.http.Gannett-Debug-Path-Full, "restarts: " req.restarts " backend: " req.backend " ttl: " beresp.ttl " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path);
    return(deliver);
}

sub vcl_hit {
#FASTLY hit
  if (!obj.cacheable) {
    return(pass);
  }
  return(deliver);
}

sub vcl_miss {
    if (req.backend.is_origin) {
        call shared_helpers_general_strip_client_ip_from_bereq;
        unset bereq.http.vcl_data;
        unset bereq.http.Gannett-FF;
        unset bereq.http.Gannett-FF-HMAC;
        unset bereq.http.Fastly-Cachetype;
        unset bereq.http.Fastly-Debug-Digest;
        unset bereq.http.Fastly-Debug-Path;
        unset bereq.http.Fastly-Debug-TTL;
        unset bereq.http.Fastly-Debug;
        unset bereq.http.GUP-Identifiers;
    }
    call set_hostheader_for_origin;
    if(req.http.Gannett-Debug){
        set bereq.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " --->";
    }

    # shared_proxy_gannett_vcl_miss_gcdn - req.http.Gannett-Custom:gcdn - clean request headers
    call shared_proxy_gannett_vcl_miss_gcdn;

    return(fetch);
}

sub vcl_deliver {

    # On non-shield nodes only and for successful HTML content responses.requests outside of the resources path,
    if (
        fastly.ff.visits_this_service == 0 &&
        resp.status == 200 && (
            std.prefixof(resp.http.Content-Type, "text/html") ||
            std.prefixof(resp.http.Content-Type, "application/json")
        )
    ) {
        call guplib_deliver_set_missing_id_cookies;
    }

    # See if something has signaled that this request should vary on User-Agent
    if (req.http.vary_on_ua_for_cam == "Yes" && resp.http.X-Content-Access-Type) {
        set resp.http.Vary:User-Agent = "";
    }
    unset req.http.vary_on_ua_for_cam;

    # WALL*LY: Make sure user is allowed to see this content.
    # Can throw faux error status 801.
    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        call wally_deliver;
    }

    # Always run cleanup after wally_deliver when on edge
    if (fastly.ff.visits_this_service < 1) {
        call wally_cleanup;
    }

    # Manage a/b test cookies and response headers.
    if(req.http.x-origin-expected-host ~ "puzzles.usatoday.com") {
        if (!req.http.Gannett-FF || req.http.Gannett-FF == server.identity) {
            call abtest_deliver;
            if (req.http.Gannett-Debug) {
                set req.http.Gannett-Debug-Path-Item = "abtest_deliver";
                call shared_helpers_general_record_object_path;
            }
        }
    }

    #CORS HEADER
    set resp.http.Access-Control-Allow-Origin = req.http.Origin;
    # used for logging purposes. unset them so clients dont see them
    set req.http.backend_ip = resp.http.backend_ip;
    unset resp.http.backend_ip;
    unset resp.http.vcl_data;
    unset resp.http.stale_from_error;

     # Remove unwanted response headers from origin / Fastly
    if (req.http.Gannett-Debug) {
        add resp.http.Server-Timing = fastly_info.state {", fastly;desc="Edge time";dur="} time.elapsed.msec;
    } else {
        unset resp.http.Gannett-Debug-Path-Full;
        unset resp.http.Gannett-Debug-Path;
        unset resp.http.Gannett-Debug-Version;
        unset resp.http.Server;
        unset resp.http.X-Powered-By;
        unset resp.http.X-Generator;
        unset resp.http.Via;
        unset resp.http.Expires;
        unset resp.http.Pragma;
        unset resp.http.X-Served-By;
        unset resp.http.X-Cache-Hits;
    }

    if (req.http.Gannett-Debug) {
        set resp.http.Gannett-Debug-Path-Full = if(resp.http.Gannett-Debug-Path-Full, " restarts: " req.restarts "visits" fastly.ff.visits_this_service " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path " >>>> " resp.http.Gannett-Debug-Path-Full, "visits" fastly.ff.visits_this_service " restarts: " req.restarts " shield: " if(req.backend.is_shield, "false", "true") " server: " server.identity  " path: " req.http.Gannett-Debug-Path);
    }
    #FASTLY deliver

    if(resp.http.Gannett-Debug-Path){
        if(resp.http.Gannett-Debug-Path == ""){
        set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path;
        }
    }
    else {
        set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path;
    }

    #### NEL reporting
    if (fastly.ff.visits_this_service == 0) {
        # Set Network Error Logging (NEL) and Reporting API headers
        # Add JS call stacks in crash reports policy if not already present
        call shared_helpers_general_deliver_set_reporting_api_headers;
    }

    # Unset guplib headers.
    unset req.http.GUP-Identifiers;

    # force set hsts header for all *.usatoday.com requests https://www.owasp.org/index.php/HTTP_Strict_Transport_Security_Cheat_Sheet
    if (req.protocol == "https") {
        if (req.http.host ~ "^(dev-|origin-staging-|staging-|ci-)?(puzzles)\.") {
            set resp.http.Strict-Transport-Security = "max-age=63072000; includeSubDomains; preload";
        }
    }

    # shared_proxy_gannett_vcl_deliver_gcdn - req.http.Gannett-Custom:gcdn - return(deliver)
    call shared_proxy_gannett_vcl_deliver_gcdn;
    # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(deliver)
    call shared_proxy_gannett_vcl_deliver_gcias;

    return(deliver);
}

sub vcl_error {
# CORS preflight response
if (obj.status == 601) {
    set obj.status = 200;
    set obj.response = "No content";
    return(deliver);
}

if (obj.status == 971) {
    set obj.http.Content-Type = "text/html; charset=utf-8";
    set obj.http.WWW-Authenticate = "Basic realm=Secured";
    set obj.status = 401;
    synthetic {"<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/1999/REC-html401-19991224/loose.dtd">
    <HTML>
    <HEAD>
    <TITLE>Error</TITLE>
    <META HTTP-EQUIV='Content-Type' CONTENT='text/html;'>
    </HEAD>
    <BODY><H1>401 Unauthorized</H1></BODY>
    </HTML>
    "};
    return (deliver);
}

/* handle 503s */
if (obj.status >= 500 && obj.status < 600) {

    /* deliver stale object if it is available */
    if (stale.exists) {
        return(deliver_stale);
    }

}
    # gannett boilerplate
  if (obj.status == 701) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 301;
    set obj.http.Cache-Control = "max-age=300";
    return(deliver);
  }
  if (obj.status == 702) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 302;
    set obj.http.Cache-Control = "no-cache";
    return(deliver);
  }
#FASTLY error

    # WALL*LY: Handle redirects to roadblock pages.  Responds to faux status 801.
    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        call wally_error;
    } else {
        # So that the WALL*LY synthetic resources can still be served when disabled.
        call wally_error_url_routing;
    }
}

sub vcl_pass {
    if (req.backend.is_origin) {
        call shared_helpers_general_strip_client_ip_from_bereq;
        unset bereq.http.vcl_data;
        unset bereq.http.Gannett-FF;
        unset bereq.http.Gannett-FF-HMAC;
        unset bereq.http.Fastly-Cachetype;
        unset bereq.http.Fastly-Debug-Digest;
        unset bereq.http.Fastly-Debug-Path;
        unset bereq.http.Fastly-Debug-TTL;
        unset bereq.http.Fastly-Debug;
    }
    call set_hostheader_for_origin;
#FASTLY pass
    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        call wally_pass;
    }
    # shared_proxy_gannett_vcl_pass_gcdn - req.http.Gannett-Custom:gcdn - return(fetch) - clean request headers
    call shared_proxy_gannett_vcl_pass_gcdn;
    # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(fetch) - set x-forwarded-for header
    call shared_proxy_gannett_vcl_pass_gcias;
}

sub vcl_log {
#FASTLY log
    if (resp.status >= 400 && resp.status < 600) {
    # all errors get logged
    call shared_helpers_general_log_bigquery;
    } else if (req.http.User-Agent ~ "Googlebot" || randombool(std.atoi(table.lookup(variables, "logging_percent_of_success")),100) ) {
    call shared_helpers_general_log_bigquery;
    }
}

# Figure out whether CAM should be enabled for this request
sub determine_cam_enabled {
    # Default to enabled
    set req.http.Gannett-Puzzles-Request-CAM = table.lookup(puzzles_cam_settings, "default_request_restrictions", "Enabled");

    # Since this subroutine will also run on shield, default to this being off
    set req.http.vary_on_ua_for_cam = "No";

    # Should only be enabled when acting as an edge
    if (fastly.ff.visits_this_service > 0) {
       set req.http.Gannett-Puzzles-Request-CAM = "Disabled (Not Edge)";
    }

    # Leave this test for last because it'll mean we're varying on User-Agent
    if (req.http.Gannett-Puzzles-Request-CAM == "Enabled") {
        # Always allow traffic from these search engines / bots through
        if (req.http.User-Agent ~ "(?i)Twitterbot|\.taboola\.|bing\.|Yahoo\.|Embedly\.|outbrain\.|google\.|AdsBot-Google\.|Googlebot\.|Yahoo!\.|MSNBot\.|Yahoo-Newscrawler\.|Slurp\.|UltraSeek\.|ArchitextSpider\.|WebCrawler\.|Googlebot|Googlebot-News|Googlebot-Image|Googlebot-Video|Googlebot-Mobile|Mediapartners-Google|Mediapartners|AdsBot-Google|bingbot|msnbot|adidxbot|\.linkedin\.com|cXensebot|facebook|FBFor|twitter|SiteArc|Screaming Frog SEO Spider|rogerbot|dotbot|Social News Desk|parse\.ly") {
            set req.http.Gannett-Puzzles-Request-CAM = "Disabled (Bot User-Agent)";
        }
        # We're taking User-Agent into account so we need to vary on it
        set req.http.vary_on_ua_for_cam = "Yes";
    }
}
