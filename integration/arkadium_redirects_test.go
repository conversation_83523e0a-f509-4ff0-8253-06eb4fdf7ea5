package integration

import (
	"net/http"
	"os"
	"testing"

	fastly "github.com/GannettDigital/go-fastly-tests"
)

func TestRedirects(t *testing.T) {
	t.<PERSON>()

	var tests = []fastly.Test{
		fastly.Header{
			Request: fastly.Request{
				Description: "puzzles.usatoday.com backend check. Should not redirect to arkadium as its from Virginia",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "",
				UA:          "",
				Referer:     "",
				KeepDomain:  true,
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"gannett-geo-ip-override": []string{
						"************",
					},
				},
			},
			Status: http.StatusOK,
			Headers: http.Header{
				"Gannett-Debug-Path-Full": []string{
					"puzzles.usatoday.com backend",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "For non-US-USERS, it should 302 puzzles.usatoday.com to arkadium",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers: http.Header{
					"gannett-geo-ip-override": []string{
						"*************",
					},
				},
			},
			Status: http.StatusFound,
			Headers: http.Header{
				"Location": []string{
					"https://games.usatoday.com/category/word",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 302 redirect for non-crossword arkadium redirects",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/category/puzzles",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusFound,
			Headers: http.Header{
				"Location": []string{
					"https://games.usatoday.com/category/puzzles",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
		fastly.Header{
			Request: fastly.Request{
				Description: "should 302 redirect for non-crossword arkadium redirects",
				Scheme:      "https://",
				Host:        "puzzles.usatoday.com",
				Path:        "/games/crescent-solitaire",
				UA:          "",
				Referer:     "",
				Cookies:     []*http.Cookie{},
				Headers:     http.Header{},
			},
			Status: http.StatusFound,
			Headers: http.Header{
				"Location": []string{
					"https://games.usatoday.com/games/crescent-solitaire",
				},
			},
			MatchType: fastly.PartialMatch{},
		},
	}

	var env = os.Getenv("ENVIRONMENT")
	for _, test := range tests {
		if env == "origin-staging" {
			continue
		}
		test.Execute(t)
	}
}
