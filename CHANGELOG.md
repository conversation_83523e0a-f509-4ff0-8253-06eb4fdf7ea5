CHANGELOG
=========

# 3.21.0
<PERSON> - PENG-28681 Enable call stacks in Reporting API crash reports (VCL header)

# 3.20.0
<PERSON> - update fastly terraform module version

# 3.19.0
gsfondouri - ORD-8491 Don't strip Fastly-Client-IP for certain backends e.g. gcdn/gciaf

# 3.18.1
<PERSON><PERSON> - do not cache for NGWAF errors (PENG-28349)

# 3.18.0
<PERSON> - update fastly test to keep use real domain (PENG-27772)

# 3.17.0
<PERSON> - Added sigsi configuration (PENG-27675)

# 3.16.0
<PERSON> - Remove SUMO logging from Group #4 (PENG-25678)

# 3.15.3
<PERSON> Weiss - Update to handle additional api endpoint for metering (SDGT-2873)

# 3.15.2
<PERSON> - Updated staging/prod backend to point to Google Cloud Run instances (SDGT-2836)

# 3.15.1
<PERSON><PERSON> - Update go-fastly-test (PENG-26190)

# 3.15.0
<PERSON> - Integrate guplib library to set anonymous IDs on all requests (GUP-6890).

# 3.14.6
<PERSON> - Added BigQuery logging (PENG-25456)

# 3.14.5
Bilal Fazal - Android App Links for puzzles.usatoday.com (PENG-25399)

# 3.14.4
<PERSON> Eich<PERSON> - Cleaned up old shared snippets (PENG-24762)

# 3.14.3
Yanyan Ni - Switch to use shared_proxy_gannett (PENG-24764)

# 3.14.2
<PERSON>yan Ni - Add no-shield backend for gcdn (PENG-24599)

# 3.14.1
Carl Eichhorn - Switched to using new shared libraries (PENG-24438)

# 3.14.0
Bilal Fazal - Add new weightshift Edge Dictionary to be used with the GHA weightshift job (PENG-24203)

# 3.13.4
Carl Eichhorn - Added shared snippets to service.tf (PENG-24346)

# 3.13.3
Carl Eichhorn - Added shared libraries to service.tf (PENG-24346)

# 3.13.2
Yanyan Ni - use gcdn and gcias subroutine in sre_shared_proxy (PENG-24142)

# 3.13.1
Yanyan Ni - add sre_shared_proxy to comment (PENG-24142)

# 3.13.0
Jay Hicks - Legacy Waf Deprecation (PENG-23668)

# 3.12.4
Yanyan Ni - use shared office acl to allow access lower environment (PENG-23796)

# 3.12.3
Yanyan Ni - Add shared_gcdn_vcl_deliver to clear cookie for gcdn (PENG-23829)

# 3.12.2
Yanyan Ni - Use reusable workflow (PENG-23719)

# 3.12.1
Yanyan Ni - Use GCDN snippett (PENG-23172)

# 3.12.0
Bilal Fazal - fix default backend only getting set to east instead of east/west

# 3.11.0
Yanyan Ni - Migrate to GHA (PENG-22196)

# 3.10.0
Jay Hicks - Update seattle shield terraform config (PENG-22762)

# 3.9.1
Tom Barber - cutover production to new SDGT hosted environment backend (SDGT-846)

# 3.9.0
Tom Barber - cutover origin-staging to new SDGT hosted environment backend (SDGT-846)

# 3.8.2
Bilal Fazal - block/redirect EU users accessing games.usatoday.com to /unsupported-eu page (PENG-22420)

# 3.8.1
Bilal Fazal - Adding redirect for non-US users (PENG-22274)

# 3.8.0
Jonathan Cross - Update WALL-LY to 10.3.2 for mobile application support (GUP-5660).

# 3.7.1
Bilal Fazal - Adding config for origin-staging host redirect (PENG-22216)

# 3.7.0
Jonathan Cross - Update WALL-LY to 10.2.0 (GUP-5336)

# 3.6.0
Jonathan Cross - Correct GUPAS backend for stage (GUP-5336)
Dan Moore - Upgrading WALL-LY to 10.0.0, which works around some Fastly behaviors to let puzzles work with POST requests.

# 3.5.0
Damian Leys - updated README.md (PENG-21405)

# 3.4.0
Bilal Fazal - removing duplicate vcl_fetch subroutine

# 3.3.0
Bilal Fazal - Adding exception for /api/query to avoid arkadium unnecessary code logic

# 3.2.0
Jonathan Cross - Implement WALL-LY integration/restrictions (GUP-5285)

# 3.1.2
Tyler Weiss - Add browser caching for webfonts specifically (SDGT-381)

# 3.1.1
Jay Hicks - Updated Geo.ip variables (PENG-21281)

# 3.1.0
Jonathan Cross - Adding GUPAS backend, WALL-LY submodule, WALL-LY VCL (GUP-5285)

# 3.0.1
Bilal Fazal - Add logic in the vcl to not override cache-control if beresp.http.cache-control already exists (PENG-21090)

# 3.0.0
Bilal Fazal - fix the frontend caching and POST requests (PENG-20976)

# 2.1.1
Yanyan Ni - AB test Subcribe Now button (PENG-20546)

# 2.1.0
Damian Leys - Change shield to IAD (PENG-20308)

# 2.0.2
Yanyan Ni - Fix cert issue

# 2.0.1
Bilal Fazal - Adding puerto rico explicitly (PENG-19272)

# 2.0.0
Bilal Fazal - Config changes to launch puzzles to 100% US users (PENG-19272)

# 1.6.2
Bilal Fazal - Adding a quick fix to redirect arkadium frontend script to games (PENG-19272)

# 1.6.3
Bilal Fazal - Adding a new dictionary for puzzles redirects (PENG-19272)

# 1.6.2
Yanyan Ni - Open new puzzles to 4 states

# 1.6.1
Yanyan Ni - Add states edge dictionary to puzzles

# 1.6.0
Bilal Fazal - changing configs to point production puzzles to puzzles.usatoday.com (PENG-18683)

# 1.5.0
Bilal Fazal - restrict new puzzles.usatoday.com experience for VPN users pre-launch (PENG-18683-launch-VPN-users)

# 1.4.0
Bilal Fazal - change arkadium redirects to use 302 instead of 301 (PENG-19029)

# 1.3.0
Bilal Fazal - Bug Fix to add authorization for unsupported path resulting in getting 404 from the new puzzles (PENG-18969)

# 1.2.0
Bilal Fazal - Support arkadium pre-launch changes redirect on puzzles.usatoday.com (PENG-18969)

# 1.1.0
Shreyas Gune - Enable WAF (PENG-18643)

# 1.0.0
Bilal Fazal - Adding arkadium redirects to handle non-crossword games plus SEO optimized redirects (PENG-18562)

# 0.4.4
Shreyas Gune - Enable Loadbalancing (PENG-18642)

# 0.4.3
Yanyan Ni - modify healthcheck

# 0.4.2
Yanyan Ni - enable hsts (PENG-18616)

# 0.4.1
Yanyan Ni - add jwt-token to cors allow header (PENG-18678)

# 0.4.0
Bilal Fazal - enable production backend, healthchecks and loadbalancing (PENG-18618)

# 0.3.0
Bilal Fazal - Adding CORS related configs (PENG-18561)

# 0.2.0
Damian Leys - Create WAF by adding WAF block to fastly_service_vcl (PENG-18619)

# 0.1.0
Bilal Fazal - Initial Release for puzzles (PENG-18561)
